#!/usr/bin/env python3
"""
Test complete scraping flow with timestamp fix
"""

import asyncio
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Change to backend directory for imports
os.chdir(backend_dir)

from app.services.facebook_scraper import FacebookScraper
from app.services.profile_manager import AntidetectProfileManager

async def test_complete_scraping_flow():
    """Test complete scraping flow with proper timestamp handling"""
    
    # Test URL
    post_url = "https://www.facebook.com/groups/comailo/posts/619541837846500/"
    profile_name = "test1"
    
    print(f"🚀 Testing Complete Scraping Flow")
    print(f"📝 Post URL: {post_url}")
    print(f"👤 Profile: {profile_name}")
    print("-" * 60)
    
    try:
        # Find test1 profile
        profiles_dir = Path(__file__).parent / "backend" / "data" / "profiles"
        test1_profile = None
        
        if profiles_dir.exists():
            for profile_dir in profiles_dir.iterdir():
                if profile_dir.is_dir() and profile_name.lower() in profile_dir.name.lower():
                    test1_profile = {
                        'name': profile_dir.name,
                        'path': str(profile_dir)
                    }
                    break
        
        if not test1_profile:
            print(f"❌ Profile '{profile_name}' not found!")
            return
        
        print(f"✅ Found profile: {test1_profile['name']}")
        profile_path = test1_profile['path']
        
        # Initialize services
        profile_manager = AntidetectProfileManager()
        facebook_scraper = FacebookScraper()
        
        # Launch browser
        print(f"🚀 Launching browser...")
        browser_result = await profile_manager.launch_browser(profile_path)
        
        if not browser_result.get('success'):
            print(f"❌ Failed to launch browser: {browser_result.get('message')}")
            return
        
        # Open Facebook
        print(f"🌐 Opening Facebook...")
        fb_result = await profile_manager.open_facebook_login(
            profile_id=test1_profile['name'],
            profile_path=profile_path
        )
        
        if not fb_result.get('success'):
            print(f"❌ Failed to open Facebook: {fb_result.get('message')}")
            await profile_manager.close_browser(test1_profile['name'])
            return
        
        # Get browser context
        camoufox_manager = profile_manager.camoufox_manager
        browser_info = camoufox_manager.active_browsers.get(test1_profile['name'])
        context = browser_info['context']
        
        # Create page and navigate
        print(f"📄 Navigating to post...")
        page = await context.new_page()
        await page.goto(post_url, wait_until='networkidle')
        await asyncio.sleep(3)
        
        # Scroll to load more comments
        print(f"📜 Scrolling to load comments...")
        for i in range(2):  # Scroll 2 times
            await page.evaluate("window.scrollBy(0, 600)")
            await asyncio.sleep(2)
        
        # Get comment elements
        comment_elements = await page.locator('div[role="article"]').all()
        print(f"📊 Found {len(comment_elements)} comment elements")
        
        # Extract comments using the improved _extract_single_page_comment method
        print(f"🔍 Extracting comments with timestamp support...")
        
        comments_data = []
        
        for i, element in enumerate(comment_elements[:5]):  # Test first 5 comments
            try:
                # Simulate the improved _extract_single_page_comment method
                comment_data = {
                    'interaction_type': 'comment',
                    'facebook_uid': '',
                    'full_name': '',
                    'profile_url': '',
                    'interaction_content': '',
                    'profile_picture_url': '',
                    'interaction_timestamp': None
                }
                
                # Extract user name
                user_name_selectors = [
                    './/span[@dir="auto" and contains(@class, "x193iq5w")]',
                    './/a[contains(@href, "/user/") or contains(@href, "/profile.php")]//span[@dir="auto"]',
                    './/span[@dir="auto" and text()]'
                ]
                
                for selector in user_name_selectors:
                    try:
                        name_element = element.locator(f"xpath={selector}").first
                        if name_element:
                            user_name = await name_element.text_content(timeout=2000)
                            if user_name and user_name.strip():
                                comment_data['full_name'] = user_name.strip()
                                break
                    except:
                        continue
                
                # Extract profile URL and UID
                user_link_selectors = [
                    './/a[contains(@href, "/groups/") and contains(@href, "/user/")]',
                    './/a[contains(@href, "/user/")]',
                    './/a[contains(@href, "/profile.php")]'
                ]
                
                for selector in user_link_selectors:
                    try:
                        link_element = element.locator(f"xpath={selector}").first
                        if link_element:
                            profile_url = await link_element.get_attribute('href', timeout=2000)
                            if profile_url:
                                comment_data['profile_url'] = profile_url
                                
                                # Extract UID
                                import re
                                uid_match = re.search(r'/user/(\d+)/?', profile_url)
                                if uid_match:
                                    comment_data['facebook_uid'] = uid_match.group(1)
                                break
                    except:
                        continue
                
                # Extract comment text
                comment_text_selectors = [
                    './/div[@dir="auto" and text() and not(.//a)]',
                    './/span[@dir="auto" and text() and not(.//a)]'
                ]
                
                for selector in comment_text_selectors:
                    try:
                        text_elements = await element.locator(f"xpath={selector}").all()
                        texts = []
                        for text_element in text_elements:
                            try:
                                text = await text_element.text_content(timeout=1500)
                                if text and text.strip():
                                    texts.append(text.strip())
                            except:
                                continue
                        
                        if texts:
                            comment_data['interaction_content'] = ' '.join(texts)
                            break
                    except:
                        continue
                
                # Extract timestamp
                timestamp_selectors = [
                    './/a[contains(@href, "comment_id")]',
                    './/span[contains(text(), "giờ") or contains(text(), "ngày") or contains(text(), "phút")]',
                    './/span[contains(text(), "hour") or contains(text(), "day") or contains(text(), "minute")]',
                    './/time',
                    './/span[@title]'
                ]
                
                for selector in timestamp_selectors:
                    try:
                        timestamp_element = element.locator(f"xpath={selector}").first
                        if timestamp_element:
                            timestamp_text = await timestamp_element.get_attribute('title', timeout=1000)
                            if not timestamp_text:
                                timestamp_text = await timestamp_element.text_content(timeout=1000)
                            
                            if timestamp_text and timestamp_text.strip():
                                comment_data['interaction_timestamp'] = timestamp_text.strip()
                                break
                    except:
                        continue
                
                # Only add if we have meaningful data
                if comment_data['facebook_uid'] or comment_data['full_name']:
                    comments_data.append(comment_data)
                    print(f"  ✅ Comment {i+1}: {comment_data['full_name']} - {comment_data.get('interaction_timestamp', 'No timestamp')}")
                    
            except Exception as e:
                print(f"  ❌ Error processing comment {i+1}: {e}")
                continue
        
        # Test data structure for database compatibility
        print(f"\n📊 TESTING DATABASE COMPATIBILITY:")
        print(f"=" * 60)
        
        for i, comment in enumerate(comments_data):
            print(f"\n📝 Comment {i+1} data structure:")
            print(f"  interaction_type: {comment.get('interaction_type', 'N/A')}")
            print(f"  facebook_uid: {comment.get('facebook_uid', 'N/A')}")
            print(f"  full_name: {comment.get('full_name', 'N/A')}")
            print(f"  profile_url: {comment.get('profile_url', 'N/A')}")
            print(f"  interaction_content: {comment.get('interaction_content', 'N/A')}")
            print(f"  interaction_timestamp: {comment.get('interaction_timestamp', 'None')}")
            print(f"  profile_picture_url: {comment.get('profile_picture_url', 'N/A')}")
            
            # Verify all required fields are present
            required_fields = ['interaction_type', 'facebook_uid', 'full_name', 'profile_url', 
                             'interaction_content', 'interaction_timestamp', 'profile_picture_url']
            
            missing_fields = [field for field in required_fields if field not in comment]
            if missing_fields:
                print(f"  ❌ Missing fields: {missing_fields}")
            else:
                print(f"  ✅ All required fields present")
        
        # Save test results
        results = {
            'post_url': post_url,
            'profile_used': test1_profile['name'],
            'total_comments': len(comments_data),
            'comments': comments_data,
            'timestamp': datetime.now().isoformat(),
            'test_status': 'success'
        }
        
        results_file = Path(__file__).parent / "complete_flow_test_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Close browser
        await page.close()
        await profile_manager.close_browser(test1_profile['name'])
        
        print(f"\n✅ COMPLETE FLOW TEST SUCCESSFUL!")
        print(f"🎯 Fixed: interaction_timestamp field properly handled")
        print(f"📈 Database save should work without KeyError")
        print(f"🔧 Extracted {len(comments_data)} comments with all required fields")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_complete_scraping_flow())
