#!/usr/bin/env python3
"""
Real Facebook scraping test with profile test1
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Change to backend directory for imports
os.chdir(backend_dir)

from app.services.profile_manager import AntidetectProfileManager

async def test_real_facebook_scraping():
    """Test real Facebook scraping with profile test1"""
    
    # Test URL
    post_url = "https://www.facebook.com/groups/comailo/posts/619541837846500/"
    profile_name = "test1"
    
    print(f"🚀 Starting Real Facebook scraping test")
    print(f"📝 Post URL: {post_url}")
    print(f"👤 Profile: {profile_name}")
    print("-" * 50)
    
    try:
        # Find test1 profile
        profiles_dir = Path(__file__).parent / "backend" / "data" / "profiles"
        test1_profile = None
        
        if profiles_dir.exists():
            for profile_dir in profiles_dir.iterdir():
                if profile_dir.is_dir() and profile_name.lower() in profile_dir.name.lower():
                    test1_profile = {
                        'name': profile_dir.name,
                        'path': str(profile_dir)
                    }
                    break
        
        if not test1_profile:
            print(f"❌ Profile '{profile_name}' not found!")
            return
        
        print(f"✅ Found profile: {test1_profile['name']}")
        profile_path = test1_profile['path']
        
        # Initialize profile manager
        profile_manager = AntidetectProfileManager()
        
        # Launch browser
        print(f"🚀 Launching browser with profile...")
        browser_result = await profile_manager.launch_browser(profile_path)
        
        if not browser_result.get('success'):
            print(f"❌ Failed to launch browser: {browser_result.get('message', 'Unknown error')}")
            return
        
        print(f"✅ Browser launched successfully")
        
        # Open Facebook
        print(f"🌐 Opening Facebook...")
        fb_result = await profile_manager.open_facebook_login(
            profile_id=test1_profile['name'],
            profile_path=profile_path
        )
        
        if not fb_result.get('success'):
            print(f"❌ Failed to open Facebook: {fb_result.get('message', 'Unknown error')}")
            await profile_manager.close_browser(test1_profile['name'])
            return
        
        print(f"✅ Facebook opened successfully")
        
        # Get browser context for manual scraping
        from app.services.camoufox_manager import CamoufoxBrowserManager
        camoufox_manager = profile_manager.camoufox_manager

        # Get the browser context using the correct profile ID
        browser_info = camoufox_manager.active_browsers.get(test1_profile['name'])
        if not browser_info:
            print(f"❌ No active browser found for profile: {test1_profile['name']}")
            print(f"Active browsers: {list(camoufox_manager.active_browsers.keys())}")
            return

        context = browser_info['context']
        
        # Create a new page for scraping
        print(f"📄 Creating new page for scraping...")
        page = await context.new_page()
        
        # Navigate to the post URL
        print(f"🔗 Navigating to post URL...")
        await page.goto(post_url, wait_until='networkidle')
        await asyncio.sleep(3)  # Wait for page to load
        
        print(f"📍 Current URL: {page.url}")
        
        # Try to find comments using the improved selectors
        print(f"🔍 Looking for comments...")
        
        # Wait for comments to load
        try:
            await page.wait_for_selector('div[role="article"]', timeout=10000)
            print(f"✅ Found article elements")
        except Exception as e:
            print(f"⚠️ No article elements found: {e}")
        
        # Get all comment elements
        comment_elements = await page.locator('div[role="article"]').all()
        print(f"📊 Found {len(comment_elements)} potential comment elements")
        
        # Test the improved selectors on first few elements
        comments_found = []
        
        for i, element in enumerate(comment_elements[:5]):  # Test first 5 elements
            print(f"\n🔍 Testing element {i+1}:")
            
            try:
                # Test user name selectors
                user_name = None
                user_name_selectors = [
                    './/span[@dir="auto" and contains(@class, "x193iq5w")]',
                    './/a[contains(@href, "/user/") or contains(@href, "/profile.php")]//span[@dir="auto"]',
                    './/span[@dir="auto" and text()]',
                    './/a[contains(@href, "facebook.com")]//span[text()]'
                ]
                
                for selector in user_name_selectors:
                    try:
                        name_element = element.locator(f"xpath={selector}").first
                        if name_element:
                            user_name = await name_element.text_content(timeout=2000)
                            if user_name and user_name.strip():
                                print(f"  👤 User name: {user_name.strip()}")
                                break
                    except:
                        continue
                
                # Test user link selectors
                profile_url = None
                user_link_selectors = [
                    './/a[contains(@href, "/groups/") and contains(@href, "/user/")]',
                    './/a[contains(@href, "/user/")]',
                    './/a[contains(@href, "/profile.php")]',
                    './/a[contains(@href, "facebook.com") and contains(@href, "/people/")]'
                ]
                
                for selector in user_link_selectors:
                    try:
                        link_element = element.locator(f"xpath={selector}").first
                        if link_element:
                            profile_url = await link_element.get_attribute('href', timeout=2000)
                            if profile_url:
                                print(f"  🔗 Profile URL: {profile_url}")
                                
                                # Extract UID
                                import re
                                uid_match = re.search(r'/user/(\d+)/?', profile_url)
                                if uid_match:
                                    print(f"  🆔 UID: {uid_match.group(1)}")
                                break
                    except:
                        continue
                
                # Test comment text selectors
                comment_text = None
                comment_text_selectors = [
                    './/div[@dir="auto" and text() and not(.//a)]',
                    './/span[@dir="auto" and text() and not(.//a)]',
                    './/div[@dir="auto"]//text()[normalize-space()]',
                    './/text()[normalize-space() and not(ancestor::a)]'
                ]
                
                for selector in comment_text_selectors:
                    try:
                        text_elements = await element.locator(f"xpath={selector}").all()
                        texts = []
                        for text_element in text_elements:
                            try:
                                text = await text_element.text_content(timeout=1500)
                                if text and text.strip():
                                    texts.append(text.strip())
                            except:
                                continue
                        
                        if texts:
                            comment_text = ' '.join(texts)
                            print(f"  💬 Comment: {comment_text[:100]}...")
                            break
                    except:
                        continue
                
                if user_name or profile_url or comment_text:
                    comments_found.append({
                        'user_name': user_name,
                        'profile_url': profile_url,
                        'comment_text': comment_text
                    })
                    
            except Exception as e:
                print(f"  ❌ Error testing element {i+1}: {e}")
        
        print(f"\n📊 Summary:")
        print(f"Total elements tested: {min(5, len(comment_elements))}")
        print(f"Comments with data found: {len(comments_found)}")
        
        for i, comment in enumerate(comments_found):
            print(f"\nComment {i+1}:")
            print(f"  Name: {comment['user_name'] or 'N/A'}")
            print(f"  URL: {comment['profile_url'] or 'N/A'}")
            print(f"  Text: {(comment['comment_text'] or 'N/A')[:100]}...")
        
        # Close page and browser
        await page.close()
        await profile_manager.close_browser(test1_profile['name'])
        
        print(f"\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_facebook_scraping())
