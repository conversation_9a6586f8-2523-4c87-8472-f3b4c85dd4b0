#!/usr/bin/env python3
"""
Test script for Facebook scraper with profile test1
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Change to backend directory for imports
os.chdir(backend_dir)

from app.services.facebook_scraper import FacebookScraper
from app.services.profile_manager import AntidetectProfileManager

async def test_facebook_scraper():
    """Test Facebook scraper with profile test1"""
    
    # Test URL
    post_url = "https://www.facebook.com/groups/comailo/posts/619541837846500/"
    profile_name = "test1"
    
    print(f"🚀 Starting Facebook scraper test")
    print(f"📝 Post URL: {post_url}")
    print(f"👤 Profile: {profile_name}")
    print("-" * 50)
    
    try:
        # Initialize scraper
        scraper = FacebookScraper()
        
        # Test profile manager first
        profile_manager = AntidetectProfileManager()

        # List available profiles by checking the profiles directory
        profiles_dir = Path(__file__).parent / "backend" / "data" / "profiles"
        available_profiles = []
        if profiles_dir.exists():
            for profile_dir in profiles_dir.iterdir():
                if profile_dir.is_dir() and profile_name.lower() in profile_dir.name.lower():
                    available_profiles.append({
                        'name': profile_dir.name,
                        'path': str(profile_dir)
                    })

        print(f"📋 Available profiles matching '{profile_name}': {[p['name'] for p in available_profiles]}")

        # Check if test1 profile exists
        test1_profile = None
        if available_profiles:
            test1_profile = available_profiles[0]  # Use first matching profile

        if not test1_profile:
            print(f"❌ Profile '{profile_name}' not found!")
            # List all profiles
            if profiles_dir.exists():
                all_profiles = [d.name for d in profiles_dir.iterdir() if d.is_dir()]
                print(f"Available profiles: {all_profiles}")
            else:
                print(f"Profiles directory not found: {profiles_dir}")
            return

        print(f"✅ Found profile: {test1_profile['name']}")
        profile_path = test1_profile['path']
        
        # Start scraping
        print(f"\n🔍 Starting scraping process...")

        # Test browser launch first
        print(f"🚀 Launching browser with profile...")
        browser_result = await profile_manager.launch_browser(profile_path)

        if browser_result.get('success'):
            print(f"✅ Browser launched successfully")

            # Test opening Facebook
            print(f"🌐 Opening Facebook...")
            fb_result = await profile_manager.open_facebook_login(
                profile_id=test1_profile['name'],
                profile_path=profile_path
            )

            if fb_result.get('success'):
                print(f"✅ Facebook opened successfully")
                print(f"📝 Message: {fb_result.get('message', 'No message')}")

                # Wait a bit for page to load
                await asyncio.sleep(5)

                # Now try to navigate to the post URL manually
                print(f"🔗 Navigating to post URL...")
                # For now, just print the URL - we'll implement actual scraping later
                print(f"Post URL: {post_url}")
                print(f"✅ Test completed - browser is ready for scraping")

                # Close browser
                print(f"🔒 Closing browser...")
                close_result = await profile_manager.close_browser(test1_profile['name'])
                print(f"Browser close result: {close_result.get('message', 'Closed')}")

                result = {
                    'status': 'success',
                    'message': 'Browser test completed successfully',
                    'data': {
                        'comments': [],
                        'browser_launched': True,
                        'facebook_opened': True
                    }
                }
            else:
                print(f"❌ Failed to open Facebook: {fb_result.get('message', 'Unknown error')}")
                result = {
                    'status': 'error',
                    'message': f"Failed to open Facebook: {fb_result.get('message', 'Unknown error')}"
                }
        else:
            print(f"❌ Failed to launch browser: {browser_result.get('message', 'Unknown error')}")
            result = {
                'status': 'error',
                'message': f"Failed to launch browser: {browser_result.get('message', 'Unknown error')}"
            }
        
        print(f"\n📊 Scraping Results:")
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Message: {result.get('message', 'No message')}")
        
        if result.get('status') == 'success':
            comments = result.get('data', {}).get('comments', [])
            print(f"Total comments found: {len(comments)}")
            
            # Show first few comments
            for i, comment in enumerate(comments[:3]):
                print(f"\n📝 Comment {i+1}:")
                print(f"  Name: {comment.get('full_name', 'N/A')}")
                print(f"  UID: {comment.get('facebook_uid', 'N/A')}")
                print(f"  Profile URL: {comment.get('profile_url', 'N/A')}")
                print(f"  Content: {comment.get('interaction_content', 'N/A')[:100]}...")
        else:
            print(f"❌ Scraping failed: {result.get('message', 'Unknown error')}")
            if 'error' in result:
                print(f"Error details: {result['error']}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_facebook_scraper())
