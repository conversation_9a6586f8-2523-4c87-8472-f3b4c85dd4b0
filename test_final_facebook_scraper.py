#!/usr/bin/env python3
"""
Final Facebook scraper test with improved selectors
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

# Change to backend directory for imports
os.chdir(backend_dir)

from app.services.profile_manager import AntidetectProfileManager

async def test_final_facebook_scraper():
    """Test final Facebook scraper with improved selectors"""
    
    # Test URL
    post_url = "https://www.facebook.com/groups/comailo/posts/619541837846500/"
    profile_name = "test1"
    
    print(f"🚀 Final Facebook Scraper Test")
    print(f"📝 Post URL: {post_url}")
    print(f"👤 Profile: {profile_name}")
    print("-" * 60)
    
    try:
        # Find test1 profile
        profiles_dir = Path(__file__).parent / "backend" / "data" / "profiles"
        test1_profile = None
        
        if profiles_dir.exists():
            for profile_dir in profiles_dir.iterdir():
                if profile_dir.is_dir() and profile_name.lower() in profile_dir.name.lower():
                    test1_profile = {
                        'name': profile_dir.name,
                        'path': str(profile_dir)
                    }
                    break
        
        if not test1_profile:
            print(f"❌ Profile '{profile_name}' not found!")
            return
        
        print(f"✅ Found profile: {test1_profile['name']}")
        profile_path = test1_profile['path']
        
        # Initialize profile manager
        profile_manager = AntidetectProfileManager()
        
        # Launch browser
        print(f"🚀 Launching browser...")
        browser_result = await profile_manager.launch_browser(profile_path)
        
        if not browser_result.get('success'):
            print(f"❌ Failed to launch browser: {browser_result.get('message')}")
            return
        
        # Open Facebook
        print(f"🌐 Opening Facebook...")
        fb_result = await profile_manager.open_facebook_login(
            profile_id=test1_profile['name'],
            profile_path=profile_path
        )
        
        if not fb_result.get('success'):
            print(f"❌ Failed to open Facebook: {fb_result.get('message')}")
            await profile_manager.close_browser(test1_profile['name'])
            return
        
        # Get browser context
        camoufox_manager = profile_manager.camoufox_manager
        browser_info = camoufox_manager.active_browsers.get(test1_profile['name'])
        context = browser_info['context']
        
        # Create page and navigate
        print(f"📄 Navigating to post...")
        page = await context.new_page()
        await page.goto(post_url, wait_until='networkidle')
        await asyncio.sleep(3)
        
        # Scroll to load more comments
        print(f"📜 Scrolling to load comments...")
        for i in range(3):  # Scroll 3 times
            await page.evaluate("window.scrollBy(0, 800)")
            await asyncio.sleep(2)
        
        # Get all comment elements
        comment_elements = await page.locator('div[role="article"]').all()
        print(f"📊 Found {len(comment_elements)} comment elements")
        
        # Extract comments using improved selectors
        comments_data = []
        
        # Improved selectors based on successful test
        user_name_selectors = [
            './/span[@dir="auto" and contains(@class, "x193iq5w")]',
            './/a[contains(@href, "/user/") or contains(@href, "/profile.php")]//span[@dir="auto"]',
            './/span[@dir="auto" and text()]'
        ]
        
        user_link_selectors = [
            './/a[contains(@href, "/groups/") and contains(@href, "/user/")]',
            './/a[contains(@href, "/user/")]',
            './/a[contains(@href, "/profile.php")]'
        ]
        
        comment_text_selectors = [
            './/div[@dir="auto" and text() and not(.//a)]',
            './/span[@dir="auto" and text() and not(.//a)]'
        ]
        
        print(f"🔍 Extracting comment data...")
        
        for i, element in enumerate(comment_elements):
            try:
                comment_data = {
                    'interaction_type': 'comment',
                    'facebook_uid': '',
                    'full_name': '',
                    'profile_url': '',
                    'interaction_content': '',
                    'profile_picture_url': ''
                }
                
                # Extract user name
                for selector in user_name_selectors:
                    try:
                        name_element = element.locator(f"xpath={selector}").first
                        if name_element:
                            user_name = await name_element.text_content(timeout=2000)
                            if user_name and user_name.strip():
                                comment_data['full_name'] = user_name.strip()
                                break
                    except:
                        continue
                
                # Extract profile URL and UID
                for selector in user_link_selectors:
                    try:
                        link_element = element.locator(f"xpath={selector}").first
                        if link_element:
                            profile_url = await link_element.get_attribute('href', timeout=2000)
                            if profile_url:
                                comment_data['profile_url'] = profile_url
                                
                                # Extract UID
                                import re
                                uid_match = re.search(r'/user/(\d+)/?', profile_url)
                                if uid_match:
                                    comment_data['facebook_uid'] = uid_match.group(1)
                                else:
                                    uid_match = re.search(r'profile\.php\?id=(\d+)', profile_url)
                                    if uid_match:
                                        comment_data['facebook_uid'] = uid_match.group(1)
                                break
                    except:
                        continue
                
                # Extract comment text
                for selector in comment_text_selectors:
                    try:
                        text_elements = await element.locator(f"xpath={selector}").all()
                        texts = []
                        for text_element in text_elements:
                            try:
                                text = await text_element.text_content(timeout=1500)
                                if text and text.strip():
                                    texts.append(text.strip())
                            except:
                                continue
                        
                        if texts:
                            comment_data['interaction_content'] = ' '.join(texts)
                            break
                    except:
                        continue
                
                # Only add if we have meaningful data
                if comment_data['facebook_uid'] or comment_data['full_name'] or comment_data['interaction_content']:
                    comments_data.append(comment_data)
                    
            except Exception as e:
                print(f"  ⚠️ Error processing element {i+1}: {e}")
                continue
        
        # Display results
        print(f"\n📊 SCRAPING RESULTS:")
        print(f"=" * 60)
        print(f"Total comments found: {len(comments_data)}")
        print(f"Post URL: {post_url}")
        print(f"Profile used: {test1_profile['name']}")
        
        for i, comment in enumerate(comments_data):
            print(f"\n📝 Comment {i+1}:")
            print(f"  👤 Name: {comment['full_name'] or 'N/A'}")
            print(f"  🆔 UID: {comment['facebook_uid'] or 'N/A'}")
            print(f"  🔗 Profile: {comment['profile_url'] or 'N/A'}")
            print(f"  💬 Content: {comment['interaction_content'] or 'N/A'}")
        
        # Save results to file
        results_file = Path(__file__).parent / "scraping_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'post_url': post_url,
                'profile_used': test1_profile['name'],
                'total_comments': len(comments_data),
                'comments': comments_data,
                'timestamp': str(asyncio.get_event_loop().time())
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # Close browser
        await page.close()
        await profile_manager.close_browser(test1_profile['name'])
        
        print(f"\n✅ SCRAPING COMPLETED SUCCESSFULLY!")
        print(f"🎯 Fixed timeout issues with improved selectors")
        print(f"📈 Successfully extracted {len(comments_data)} comments")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_facebook_scraper())
